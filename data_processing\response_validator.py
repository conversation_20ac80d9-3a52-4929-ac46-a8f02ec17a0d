"""
应答验证器模块 - 基于JSON配置的动态应答验证引擎

该模块实现了完全基于JSON配置驱动的应答验证功能，支持任意自由串口协议的应答验证。
核心特性：
- 动态配置驱动：所有验证逻辑完全由JSON配置决定，零硬编码
- 多种验证策略：支持精确匹配和正则表达式验证
- 超时重试机制：可配置的超时时间和重试次数
- 验证结果缓存：提高验证性能
- 协议无关：完全不依赖具体协议实现

作者: DataStudio开发团队
创建时间: 2025-08-07
"""

import logging
import re
import time
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
from utils.helper_utils import hex_to_bytes, bytes_to_hex
from utils.exceptions import DataProcessingError


class ValidationResult(Enum):
    """验证结果枚举"""
    SUCCESS = "success"         # 验证成功
    FAILED = "failed"          # 验证失败
    TIMEOUT = "timeout"        # 超时
    ERROR = "error"            # 错误


@dataclass
class ValidationConfig:
    """验证配置数据类"""
    validation_type: str    # 验证类型：exact 或 regex
    pattern: str           # 验证模式
    timeout: float         # 超时时间（秒）
    retry_count: int       # 重试次数
    
    def __post_init__(self):
        """初始化后验证"""
        if self.validation_type not in ['exact', 'regex']:
            raise ValueError(f"不支持的验证类型: {self.validation_type}")
        if self.timeout <= 0:
            raise ValueError("超时时间必须大于0")
        if self.retry_count < 0:
            raise ValueError("重试次数不能为负数")


@dataclass
class ValidationResponse:
    """验证响应数据类"""
    result: ValidationResult  # 验证结果
    matched: bool            # 是否匹配
    response_data: bytes     # 应答数据
    pattern_used: str        # 使用的模式
    validation_time: float   # 验证耗时
    retry_count: int         # 实际重试次数
    error_message: str       # 错误信息


class ResponseValidator:
    """
    基于JSON配置的动态应答验证器
    
    核心功能：
    1. 精确匹配验证
    2. 正则表达式验证
    3. 超时重试机制
    4. 验证结果缓存
    """
    
    def __init__(self):
        """初始化应答验证器"""
        self.logger = logging.getLogger(f"{__name__}.ResponseValidator")
        
        # 预编译的正则表达式缓存
        self._regex_cache = {}
        
        # 统计信息
        self.stats = {
            "validations_performed": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "timeout_validations": 0,
            "error_validations": 0,
            "total_retry_count": 0,
            "cache_hits": 0
        }
        
        self.logger.info("应答验证器初始化完成")
    
    def validate_response(self, response_data: bytes, config: ValidationConfig) -> ValidationResponse:
        """
        验证应答数据
        
        Args:
            response_data: 应答数据
            config: 验证配置
            
        Returns:
            验证响应结果
        """
        start_time = time.time()
        self.stats["validations_performed"] += 1
        
        try:
            if config.validation_type == "exact":
                result = self._validate_exact_match(response_data, config)
            elif config.validation_type == "regex":
                result = self._validate_regex_match(response_data, config)
            else:
                raise ValueError(f"不支持的验证类型: {config.validation_type}")
            
            # 更新统计信息
            if result.result == ValidationResult.SUCCESS:
                self.stats["successful_validations"] += 1
            elif result.result == ValidationResult.FAILED:
                self.stats["failed_validations"] += 1
            elif result.result == ValidationResult.TIMEOUT:
                self.stats["timeout_validations"] += 1
            elif result.result == ValidationResult.ERROR:
                self.stats["error_validations"] += 1
            
            self.stats["total_retry_count"] += result.retry_count
            
            return result
            
        except Exception as e:
            self.logger.error(f"验证过程发生错误: {str(e)}")
            self.stats["error_validations"] += 1
            
            return ValidationResponse(
                result=ValidationResult.ERROR,
                matched=False,
                response_data=response_data,
                pattern_used=config.pattern,
                validation_time=time.time() - start_time,
                retry_count=0,
                error_message=str(e)
            )
    
    def _validate_exact_match(self, response_data: bytes, config: ValidationConfig) -> ValidationResponse:
        """
        精确匹配验证
        
        Args:
            response_data: 应答数据
            config: 验证配置
            
        Returns:
            验证响应结果
        """
        start_time = time.time()
        
        try:
            # 将模式转换为字节数据
            expected_bytes = hex_to_bytes(config.pattern)
            
            # 执行精确匹配
            matched = response_data == expected_bytes
            
            result = ValidationResult.SUCCESS if matched else ValidationResult.FAILED
            
            self.logger.debug(f"精确匹配验证 - 期望: {bytes_to_hex(expected_bytes)}, "
                            f"实际: {bytes_to_hex(response_data)}, 结果: {matched}")
            
            return ValidationResponse(
                result=result,
                matched=matched,
                response_data=response_data,
                pattern_used=config.pattern,
                validation_time=time.time() - start_time,
                retry_count=0,
                error_message="" if matched else "精确匹配失败"
            )
            
        except Exception as e:
            self.logger.error(f"精确匹配验证失败: {str(e)}")
            return ValidationResponse(
                result=ValidationResult.ERROR,
                matched=False,
                response_data=response_data,
                pattern_used=config.pattern,
                validation_time=time.time() - start_time,
                retry_count=0,
                error_message=f"精确匹配验证错误: {str(e)}"
            )
    
    def _validate_regex_match(self, response_data: bytes, config: ValidationConfig) -> ValidationResponse:
        """
        正则表达式验证
        
        Args:
            response_data: 应答数据
            config: 验证配置
            
        Returns:
            验证响应结果
        """
        start_time = time.time()
        
        try:
            # 将应答数据转换为十六进制字符串
            hex_string = bytes_to_hex(response_data, separator=" ")
            
            # 获取预编译的正则表达式
            regex_pattern = self._get_compiled_regex(config.pattern)
            
            # 执行正则表达式匹配
            match = regex_pattern.search(hex_string)
            matched = match is not None
            
            self.logger.debug(f"正则表达式验证 - 模式: {config.pattern}, "
                            f"数据: {hex_string}, 结果: {matched}")
            
            result = ValidationResult.SUCCESS if matched else ValidationResult.FAILED
            
            return ValidationResponse(
                result=result,
                matched=matched,
                response_data=response_data,
                pattern_used=config.pattern,
                validation_time=time.time() - start_time,
                retry_count=0,
                error_message="" if matched else "正则表达式匹配失败"
            )
            
        except Exception as e:
            self.logger.error(f"正则表达式验证失败: {str(e)}")
            return ValidationResponse(
                result=ValidationResult.ERROR,
                matched=False,
                response_data=response_data,
                pattern_used=config.pattern,
                validation_time=time.time() - start_time,
                retry_count=0,
                error_message=f"正则表达式验证错误: {str(e)}"
            )
    
    def _get_compiled_regex(self, pattern: str) -> re.Pattern:
        """
        获取预编译的正则表达式
        
        Args:
            pattern: 正则表达式模式
            
        Returns:
            编译后的正则表达式对象
        """
        if pattern not in self._regex_cache:
            try:
                compiled_regex = re.compile(pattern, re.IGNORECASE)
                self._regex_cache[pattern] = compiled_regex
                self.logger.debug(f"正则表达式已编译并缓存: {pattern}")
            except re.error as e:
                raise ValueError(f"无效的正则表达式: {pattern}, 错误: {str(e)}")
        else:
            self.stats["cache_hits"] += 1
        
        return self._regex_cache[pattern]
    
    def validate_with_retry(self, response_data: bytes, config: ValidationConfig, 
                          retry_callback=None) -> ValidationResponse:
        """
        带重试机制的验证
        
        Args:
            response_data: 应答数据
            config: 验证配置
            retry_callback: 重试回调函数，用于获取新的应答数据
            
        Returns:
            验证响应结果
        """
        start_time = time.time()
        last_response = None
        
        for attempt in range(config.retry_count + 1):  # +1 因为包含初始尝试
            try:
                # 第一次使用提供的数据，后续使用回调获取新数据
                if attempt == 0:
                    current_data = response_data
                elif retry_callback:
                    current_data = retry_callback()
                    if current_data is None:
                        break
                else:
                    # 没有重试回调，使用原始数据
                    current_data = response_data
                
                # 执行验证
                result = self.validate_response(current_data, config)
                
                # 如果验证成功，返回结果
                if result.result == ValidationResult.SUCCESS:
                    result.retry_count = attempt
                    result.validation_time = time.time() - start_time
                    return result
                
                last_response = result
                
                # 如果不是最后一次尝试，等待一小段时间再重试
                if attempt < config.retry_count:
                    time.sleep(0.1)  # 100ms延迟
                    
            except Exception as e:
                self.logger.error(f"重试验证第 {attempt + 1} 次失败: {str(e)}")
                last_response = ValidationResponse(
                    result=ValidationResult.ERROR,
                    matched=False,
                    response_data=response_data,
                    pattern_used=config.pattern,
                    validation_time=time.time() - start_time,
                    retry_count=attempt,
                    error_message=str(e)
                )
        
        # 所有重试都失败了
        if last_response:
            last_response.retry_count = config.retry_count
            last_response.validation_time = time.time() - start_time
            return last_response
        else:
            return ValidationResponse(
                result=ValidationResult.FAILED,
                matched=False,
                response_data=response_data,
                pattern_used=config.pattern,
                validation_time=time.time() - start_time,
                retry_count=config.retry_count,
                error_message="所有重试都失败"
            )
    
    def create_validation_config(self, config_dict: Dict[str, Any]) -> ValidationConfig:
        """
        从字典创建验证配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            验证配置对象
        """
        try:
            return ValidationConfig(
                validation_type=config_dict["type"],
                pattern=config_dict["pattern"],
                timeout=config_dict["timeout"],
                retry_count=config_dict["retry_count"]
            )
        except Exception as e:
            raise DataProcessingError(
                f"验证配置创建失败: {str(e)}",
                error_code="VALIDATION_CONFIG_ERROR",
                details={"config": config_dict}
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算成功率
        total_validations = stats["validations_performed"]
        if total_validations > 0:
            stats["success_rate"] = stats["successful_validations"] / total_validations * 100
            stats["average_retry_count"] = stats["total_retry_count"] / total_validations
        else:
            stats["success_rate"] = 0.0
            stats["average_retry_count"] = 0.0
        
        # 缓存命中率
        if stats["validations_performed"] > 0:
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["validations_performed"] * 100
        else:
            stats["cache_hit_rate"] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "validations_performed": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "timeout_validations": 0,
            "error_validations": 0,
            "total_retry_count": 0,
            "cache_hits": 0
        }
        self.logger.info("统计信息已重置")
    
    def clear_regex_cache(self):
        """清空正则表达式缓存"""
        self._regex_cache.clear()
        self.logger.info("正则表达式缓存已清空")
