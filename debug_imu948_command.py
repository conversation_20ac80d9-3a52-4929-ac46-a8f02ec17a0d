#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IMU948指令调试脚本
专门用于调试disable_auto_report指令执行失败的问题

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from business_logic.protocol_flow_controller import ProtocolFlowController
from business_logic.logger_manager import LoggerManager, LogLevel, LogFormat


def debug_imu948_command():
    """调试IMU948指令执行"""
    print("🔍 IMU948指令调试程序")
    print("=" * 60)
    
    # 初始化日志管理器 - 设置为DEBUG级别以显示详细日志
    log_config = {
        "log_level": LogLevel.DEBUG,  # 改为DEBUG级别
        "log_format": LogFormat.DETAILED,
        "log_dir": "logs",
        "enable_console": True,
        "enable_file": True,
        "enable_performance": True
    }
    logger_manager = LoggerManager(log_config)
    logger = logger_manager.get_logger("IMU948Debug")
    
    config_path = "config/protocols/imu948_example.json"
    
    try:
        # 检查配置文件
        config_file = Path(config_path)
        if not config_file.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        print(f"✅ 配置文件检查通过: {config_path}")
        
        # 初始化协议流程控制器
        logger.info("正在初始化协议流程控制器...")
        flow_controller = ProtocolFlowController(config_path)
        
        # 获取协议信息
        protocol_info = flow_controller.get_protocol_info()
        print(f"📋 协议信息:")
        print(f"   名称: {protocol_info['name']}")
        print(f"   串口: {protocol_info['serial_config']['port']}")
        print(f"   波特率: {protocol_info['serial_config']['baudrate']}")
        
        # 连接设备
        print("\n🔌 正在连接设备...")
        try:
            if not flow_controller.connect():
                print("❌ 设备连接失败")
                print("💡 可能的原因:")
                print("   1. 串口被其他程序占用")
                print("   2. 设备未连接")
                print("   3. 串口权限问题")
                print("   4. 串口配置错误")
                return False
        except Exception as e:
            print(f"❌ 设备连接异常: {str(e)}")
            if "PermissionError" in str(e) or "拒绝访问" in str(e):
                print("💡 串口被占用，请:")
                print("   1. 关闭其他可能使用COM6的程序")
                print("   2. 重新插拔设备")
                print("   3. 检查设备管理器中的串口状态")
            return False

        print("✅ 设备连接成功")
        
        # 执行单个指令测试
        print("\n🧪 开始测试disable_auto_report指令...")
        print("=" * 60)
        
        # 获取指令执行器
        command_executor = flow_controller.command_executor
        
        # 执行disable_auto_report指令
        result = command_executor.execute_command("disable_auto_report")
        
        print("\n📊 指令执行结果:")
        print(f"   指令ID: {result.command_id}")
        print(f"   指令名称: {result.command_name}")
        print(f"   执行成功: {result.success}")
        print(f"   执行时间: {result.execution_time:.3f}s")
        print(f"   重试次数: {result.retry_count}")
        
        if result.send_data:
            from utils.helper_utils import bytes_to_hex
            print(f"   发送数据: {bytes_to_hex(result.send_data)}")
        
        if result.response_data:
            from utils.helper_utils import bytes_to_hex
            print(f"   接收数据: {bytes_to_hex(result.response_data)}")
        
        if result.validation_result:
            print(f"   验证结果: {result.validation_result.result.value}")
            print(f"   验证匹配: {result.validation_result.matched}")
            if result.validation_result.error_message:
                print(f"   验证错误: {result.validation_result.error_message}")
        
        if result.error_message:
            print(f"   错误信息: {result.error_message}")
        
        # 断开连接
        flow_controller.disconnect()
        print("\n✅ 设备已断开连接")
        
        return result.success
        
    except Exception as e:
        logger.error(f"调试程序异常: {str(e)}")
        print(f"❌ 调试程序异常: {str(e)}")
        return False
    
    finally:
        logger_manager.shutdown()


if __name__ == "__main__":
    print("🚀 启动IMU948指令调试...")
    success = debug_imu948_command()
    
    if success:
        print("\n🎉 指令执行成功！")
        sys.exit(0)
    else:
        print("\n❌ 指令执行失败，请查看详细日志")
        sys.exit(1)
