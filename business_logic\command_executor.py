"""
指令执行管理器 - 业务逻辑层组件

该模块实现了配置驱动的指令执行管理，负责：
- 异步指令执行和超时重试机制
- 指令队列管理和优先级处理
- 指令执行结果的统计和监控
- 串口通信协调

核心特性：
- 动态配置驱动：所有指令逻辑完全由JSON配置决定
- 异步执行：支持异步指令执行和并发控制
- 重试机制：智能的超时重试和错误恢复
- 统计监控：详细的执行统计和性能监控

作者: DataStudio开发团队
创建时间: 2025-08-08
"""

import logging
import time
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass
from queue import Queue, Empty

from utils.serial_config_manager import SerialConfigManager
from utils.helper_utils import hex_to_bytes, bytes_to_hex
from utils.exceptions import DataProcessingError
from communication.serial_manager import SerialManager
from data_processing.response_validator import ResponseValidator, ValidationConfig, ValidationResult, ValidationResponse


@dataclass
class CommandResult:
    """指令执行结果"""
    command_id: str          # 指令ID
    command_name: str        # 指令名称
    send_data: bytes         # 发送的数据
    response_data: bytes     # 接收的应答数据
    validation_result: ValidationResponse  # 验证结果
    execution_time: float    # 执行耗时
    success: bool           # 是否成功
    retry_count: int = 0    # 重试次数
    error_message: Optional[str] = None  # 错误信息


@dataclass
class CommandRequest:
    """指令执行请求"""
    command_id: str          # 指令ID
    priority: int = 0        # 优先级（数字越大优先级越高）
    timeout: Optional[float] = None  # 超时时间
    retry_count: Optional[int] = None  # 重试次数
    callback: Optional[callable] = None  # 回调函数


class CommandExecutor:
    """
    指令执行管理器
    
    核心功能：
    1. 配置驱动的指令执行
    2. 异步指令执行和超时重试
    3. 指令队列管理和优先级处理
    4. 执行结果统计和监控
    """
    
    def __init__(self, config_manager: SerialConfigManager, 
                 serial_manager: SerialManager,
                 response_validator: ResponseValidator):
        """
        初始化指令执行管理器
        
        Args:
            config_manager: 配置管理器
            serial_manager: 串口管理器
            response_validator: 应答验证器
        """
        self.logger = logging.getLogger(f"{__name__}.CommandExecutor")
        
        # 组件引用
        self.config_manager = config_manager
        self.serial_manager = serial_manager
        self.response_validator = response_validator
        
        # 指令队列
        self.command_queue = Queue()
        self.result_queue = Queue()
        
        # 线程管理
        self.executor_thread = None
        self.stop_event = threading.Event()
        self.is_running = False
        
        # 统计信息
        self.stats = {
            "commands_executed": 0,
            "commands_successful": 0,
            "commands_failed": 0,
            "total_execution_time": 0.0,
            "total_retry_count": 0,
            "queue_size": 0,
            "max_queue_size": 0
        }
        
        self.logger.info("指令执行管理器初始化完成")
    
    def start(self):
        """启动指令执行器"""
        if self.is_running:
            self.logger.warning("指令执行器已经在运行中")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # 启动执行线程
        self.executor_thread = threading.Thread(
            target=self._executor_worker,
            daemon=True
        )
        self.executor_thread.start()
        
        self.logger.info("指令执行器已启动")
    
    def stop(self):
        """停止指令执行器"""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        if self.executor_thread and self.executor_thread.is_alive():
            self.executor_thread.join(timeout=5.0)
        
        self.logger.info("指令执行器已停止")
    
    def execute_command(self, command_id: str, timeout: float = None, 
                       retry_count: int = None) -> CommandResult:
        """
        同步执行单个指令
        
        Args:
            command_id: 指令ID
            timeout: 超时时间（秒）
            retry_count: 重试次数
            
        Returns:
            指令执行结果
        """
        start_time = time.time()
        
        # 查找指令配置
        command = self.config_manager.get_command_by_id(command_id)
        if not command:
            raise DataProcessingError(
                f"指令不存在: {command_id}",
                error_code="COMMAND_NOT_FOUND",
                details={"command_id": command_id}
            )
        
        # 使用配置中的默认值
        if timeout is None:
            timeout = command.response_validation.timeout
        if retry_count is None:
            retry_count = command.response_validation.retry_count
        
        current_retry = 0
        last_error = None
        
        while current_retry <= retry_count:
            try:
                # 执行指令
                result = self._execute_single_command(command, timeout)
                
                # 计算总执行时间
                total_time = time.time() - start_time
                result.execution_time = total_time
                result.retry_count = current_retry
                
                # 更新统计信息
                self._update_stats(result)
                
                if result.success:
                    self.logger.info(f"指令执行成功: {command_id}, 重试次数: {current_retry}, 耗时: {total_time:.3f}s")
                    return result
                else:
                    last_error = f"应答验证失败: {result.validation_result.error_message}"
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"指令执行失败: {command_id}, 重试 {current_retry}/{retry_count}, 错误: {last_error}")
            
            current_retry += 1
            
            # 重试前短暂等待
            if current_retry <= retry_count:
                time.sleep(0.1)
        
        # 所有重试都失败
        total_time = time.time() - start_time
        
        # 创建失败的验证结果
        failed_validation = ValidationResponse(
            result=ValidationResult.FAILED,
            matched=False,
            response_data=b'',
            pattern_used=command.response_validation.pattern,
            validation_time=0.0,
            retry_count=current_retry - 1,
            error_message=last_error or "验证失败"
        )

        failed_result = CommandResult(
            command_id=command.id,
            command_name=command.name,
            send_data=hex_to_bytes(command.send),
            response_data=b'',
            validation_result=failed_validation,
            execution_time=total_time,
            success=False,
            retry_count=current_retry - 1,
            error_message=last_error
        )
        
        # 更新统计信息
        self._update_stats(failed_result)
        
        self.logger.error(f"指令执行最终失败: {command_id}, 重试次数: {current_retry - 1}, 最后错误: {last_error}")
        return failed_result
    
    def execute_command_async(self, command_id: str, priority: int = 0,
                            timeout: float = None, retry_count: int = None,
                            callback: callable = None) -> bool:
        """
        异步执行指令
        
        Args:
            command_id: 指令ID
            priority: 优先级（数字越大优先级越高）
            timeout: 超时时间
            retry_count: 重试次数
            callback: 完成回调函数
            
        Returns:
            是否成功加入队列
        """
        if not self.is_running:
            self.logger.error("指令执行器未启动，无法执行异步指令")
            return False
        
        request = CommandRequest(
            command_id=command_id,
            priority=priority,
            timeout=timeout,
            retry_count=retry_count,
            callback=callback
        )
        
        try:
            self.command_queue.put(request)
            
            # 更新队列统计
            current_size = self.command_queue.qsize()
            self.stats["queue_size"] = current_size
            if current_size > self.stats["max_queue_size"]:
                self.stats["max_queue_size"] = current_size
            
            self.logger.debug(f"指令已加入异步执行队列: {command_id}, 优先级: {priority}")
            return True
            
        except Exception as e:
            self.logger.error(f"指令加入队列失败: {command_id}, 错误: {str(e)}")
            return False
    
    def _execute_single_command(self, command, timeout: float) -> CommandResult:
        """
        执行单个指令（内部方法）
        
        Args:
            command: 指令配置对象
            timeout: 超时时间
            
        Returns:
            指令执行结果
        """
        start_time = time.time()
        
        try:
            # 准备发送数据
            send_data = hex_to_bytes(command.send)
            
            # 发送指令
            send_success = self.serial_manager.write_data(send_data)
            if not send_success:
                raise DataProcessingError(f"指令发送失败: {command.id}")
            
            # 接收应答
            response_data = self.serial_manager.read_data(timeout=timeout)
            if not response_data:
                response_data = b''
            
            # 验证应答
            validation_config = ValidationConfig(
                validation_type=command.response_validation.type,
                pattern=command.response_validation.pattern,
                timeout=timeout,
                retry_count=0  # 这里不重试，重试在上层处理
            )
            
            validation_result = self.response_validator.validate_response(response_data, validation_config)

            # 计算执行时间
            execution_time = time.time() - start_time

            result = CommandResult(
                command_id=command.id,
                command_name=command.name,
                send_data=send_data,
                response_data=response_data,
                validation_result=validation_result,
                execution_time=execution_time,
                success=validation_result.matched
            )
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time

            # 创建错误的验证结果
            error_validation = ValidationResponse(
                result=ValidationResult.ERROR,
                matched=False,
                response_data=b'',
                pattern_used=command.response_validation.pattern,
                validation_time=0.0,
                retry_count=0,
                error_message=str(e)
            )

            result = CommandResult(
                command_id=command.id,
                command_name=command.name,
                send_data=hex_to_bytes(command.send),
                response_data=b'',
                validation_result=error_validation,
                execution_time=execution_time,
                success=False,
                error_message=str(e)
            )
            
            return result
    
    def _executor_worker(self):
        """指令执行工作线程"""
        self.logger.info("指令执行工作线程启动")
        
        try:
            while not self.stop_event.is_set():
                try:
                    # 获取指令请求（带超时）
                    request = self.command_queue.get(timeout=1.0)
                    
                    # 更新队列大小统计
                    self.stats["queue_size"] = self.command_queue.qsize()
                    
                    # 执行指令
                    result = self.execute_command(
                        request.command_id,
                        request.timeout,
                        request.retry_count
                    )
                    
                    # 调用回调函数
                    if request.callback:
                        try:
                            request.callback(result)
                        except Exception as e:
                            self.logger.error(f"指令回调函数执行异常: {str(e)}")
                    
                    # 将结果放入结果队列
                    self.result_queue.put(result)
                    
                except Empty:
                    # 队列为空，继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"指令执行工作线程异常: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"指令执行工作线程严重异常: {str(e)}")
        finally:
            self.logger.info("指令执行工作线程结束")
    
    def _update_stats(self, result: CommandResult):
        """更新统计信息"""
        self.stats["commands_executed"] += 1
        self.stats["total_execution_time"] += result.execution_time
        self.stats["total_retry_count"] += result.retry_count
        
        if result.success:
            self.stats["commands_successful"] += 1
        else:
            self.stats["commands_failed"] += 1
    
    def get_result(self, timeout: float = None) -> Optional[CommandResult]:
        """
        获取异步执行结果
        
        Args:
            timeout: 超时时间
            
        Returns:
            指令执行结果，如果没有结果则返回None
        """
        try:
            return self.result_queue.get(timeout=timeout)
        except Empty:
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算成功率
        if stats["commands_executed"] > 0:
            stats["success_rate"] = stats["commands_successful"] / stats["commands_executed"] * 100
            stats["average_execution_time"] = stats["total_execution_time"] / stats["commands_executed"]
            stats["average_retry_count"] = stats["total_retry_count"] / stats["commands_executed"]
        else:
            stats["success_rate"] = 0.0
            stats["average_execution_time"] = 0.0
            stats["average_retry_count"] = 0.0
        
        # 添加状态信息
        stats["is_running"] = self.is_running
        stats["current_queue_size"] = self.command_queue.qsize()
        stats["result_queue_size"] = self.result_queue.qsize()
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            "commands_executed": 0,
            "commands_successful": 0,
            "commands_failed": 0,
            "total_execution_time": 0.0,
            "total_retry_count": 0,
            "queue_size": 0,
            "max_queue_size": 0
        }
        
        self.logger.info("指令执行器统计信息已重置")
    
    def clear_queues(self):
        """清空所有队列"""
        # 清空指令队列
        while not self.command_queue.empty():
            try:
                self.command_queue.get_nowait()
            except Empty:
                break
        
        # 清空结果队列
        while not self.result_queue.empty():
            try:
                self.result_queue.get_nowait()
            except Empty:
                break
        
        # 重置队列统计
        self.stats["queue_size"] = 0
        self.stats["max_queue_size"] = 0
        
        self.logger.info("指令执行器队列已清空")
    
    def get_queue_status(self) -> Dict[str, int]:
        """获取队列状态"""
        return {
            "command_queue_size": self.command_queue.qsize(),
            "result_queue_size": self.result_queue.qsize(),
            "max_queue_size": self.stats["max_queue_size"]
        }