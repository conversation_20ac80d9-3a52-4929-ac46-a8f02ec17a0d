#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口管理器 - 协议无关的串口底层操作
提供统一的串口连接、断开、读写接口，完全不依赖具体协议实现

核心特性:
- 协议无关性: 纯粹的串口底层操作，不包含协议逻辑
- 线程安全: 支持多线程环境下的安全操作
- 异常处理: 完善的错误恢复和状态管理机制
- 状态监控: 连接状态的实时监控和异常恢复
- 配置驱动: 基于SerialConfig的动态配置

作者: LD (Lead Developer)
创建时间: 2025-08-07
版本: 1.0
"""

import serial
import threading
import time
from typing import Optional, Dict, Any, Callable
from enum import Enum
import logging

from utils.serial_config_manager import SerialConfig
from utils.exceptions import (
    SerialConnectionError, SerialTimeoutError, SerialWriteError,
    SerialReadError, InitializationError, ResourceNotAvailableError
)
from utils.constants import SystemState


class ConnectionState(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    ERROR = "error"


class SerialManager:
    """
    串口管理器 - 协议无关的串口底层操作
    
    提供统一的串口连接、断开、读写接口，支持连接状态监控和异常恢复。
    完全不依赖具体协议实现，为上层提供纯粹的串口通信服务。
    """
    
    def __init__(self, config: SerialConfig, name: str = "SerialManager"):
        """
        初始化串口管理器
        
        Args:
            config: 串口配置对象
            name: 管理器名称，用于日志标识
        """
        self._config = config
        self._name = name
        self._logger = logging.getLogger(f"datastudio.communication.{name}")
        
        # 串口连接对象
        self._serial_connection: Optional[serial.Serial] = None
        
        # 状态管理
        self._connection_state = ConnectionState.DISCONNECTED
        self._last_error: Optional[Exception] = None
        self._connection_attempts = 0
        self._last_connection_time: Optional[float] = None
        
        # 线程安全
        self._lock = threading.RLock()
        self._read_lock = threading.Lock()
        self._write_lock = threading.Lock()
        
        # 状态回调
        self._state_callbacks: Dict[str, Callable] = {}
        
        # 统计信息
        self._stats = {
            'bytes_sent': 0,
            'bytes_received': 0,
            'connection_count': 0,
            'error_count': 0,
            'last_activity': None
        }
        
        self._logger.info(f"串口管理器初始化完成: {self._name}")
    
    def connect(self, timeout: float = 5.0) -> bool:
        """
        连接串口
        
        Args:
            timeout: 连接超时时间（秒）
            
        Returns:
            连接是否成功
            
        Raises:
            SerialConnectionError: 连接失败
            InitializationError: 初始化失败
        """
        with self._lock:
            if self._connection_state == ConnectionState.CONNECTED:
                self._logger.warning(f"串口已连接: {self._config.port}")
                return True
            
            self._set_connection_state(ConnectionState.CONNECTING)
            
            try:
                self._logger.info(f"正在连接串口: {self._config.port}")
                
                # 创建串口连接
                self._serial_connection = serial.Serial(
                    port=self._config.port,
                    baudrate=self._config.baudrate,
                    bytesize=self._config.databits,
                    parity=self._get_parity_constant(self._config.parity),
                    stopbits=self._config.stopbits,
                    timeout=self._config.timeout,
                    write_timeout=timeout
                )
                
                # 验证连接
                if not self._serial_connection.is_open:
                    self._serial_connection.open()
                
                # 清空缓冲区
                self._serial_connection.reset_input_buffer()
                self._serial_connection.reset_output_buffer()
                
                # 更新状态
                self._set_connection_state(ConnectionState.CONNECTED)
                self._last_connection_time = time.time()
                self._connection_attempts = 0
                self._stats['connection_count'] += 1
                
                self._logger.info(f"串口连接成功: {self._config.port}")
                return True
                
            except serial.SerialException as e:
                self._handle_connection_error(e)
                raise SerialConnectionError(self._config.port, f"串口连接失败: {e}")
            except Exception as e:
                self._handle_connection_error(e)
                raise InitializationError("SerialManager", f"串口初始化失败: {e}")
    
    def disconnect(self) -> bool:
        """
        断开串口连接
        
        Returns:
            断开是否成功
        """
        with self._lock:
            if self._connection_state == ConnectionState.DISCONNECTED:
                self._logger.warning("串口已断开")
                return True
            
            try:
                self._logger.info(f"正在断开串口: {self._config.port}")
                
                if self._serial_connection and self._serial_connection.is_open:
                    # 清空缓冲区
                    self._serial_connection.reset_input_buffer()
                    self._serial_connection.reset_output_buffer()
                    
                    # 关闭连接
                    self._serial_connection.close()
                
                self._serial_connection = None
                self._set_connection_state(ConnectionState.DISCONNECTED)
                
                self._logger.info(f"串口断开成功: {self._config.port}")
                return True
                
            except Exception as e:
                self._logger.error(f"串口断开失败: {e}")
                self._stats['error_count'] += 1
                return False
    
    def write_data(self, data: bytes) -> int:
        """
        写入数据到串口
        
        Args:
            data: 要写入的字节数据
            
        Returns:
            实际写入的字节数
            
        Raises:
            SerialWriteError: 写入失败
            ResourceNotAvailableError: 串口未连接
        """
        if not self.is_connected():
            raise ResourceNotAvailableError("串口连接", "串口未连接，无法写入数据")
        
        with self._write_lock:
            try:
                bytes_written = self._serial_connection.write(data)
                self._serial_connection.flush()  # 确保数据发送
                
                # 更新统计
                self._stats['bytes_sent'] += bytes_written
                self._stats['last_activity'] = time.time()
                
                self._logger.debug(f"写入数据成功: {bytes_written} 字节")
                return bytes_written
                
            except serial.SerialTimeoutException as e:
                raise SerialTimeoutError("write", self._config.timeout)
            except serial.SerialException as e:
                self._handle_connection_error(e)
                raise SerialWriteError(data.hex(), f"串口写入失败: {e}")
            except Exception as e:
                self._stats['error_count'] += 1
                raise SerialWriteError(data.hex(), f"写入数据时发生未知错误: {e}")
    
    def read_data(self, size: int = 1, timeout: Optional[float] = None) -> bytes:
        """
        从串口读取数据
        
        Args:
            size: 要读取的字节数
            timeout: 读取超时时间，None使用配置的超时时间
            
        Returns:
            读取到的字节数据
            
        Raises:
            SerialReadError: 读取失败
            ResourceNotAvailableError: 串口未连接
        """
        if not self.is_connected():
            raise ResourceNotAvailableError("串口连接", "串口未连接，无法读取数据")
        
        with self._read_lock:
            try:
                # 临时设置超时时间
                original_timeout = self._serial_connection.timeout
                if timeout is not None:
                    self._serial_connection.timeout = timeout
                
                data = self._serial_connection.read(size)
                
                # 恢复原始超时时间
                if timeout is not None:
                    self._serial_connection.timeout = original_timeout
                
                # 更新统计
                if data:
                    self._stats['bytes_received'] += len(data)
                    self._stats['last_activity'] = time.time()
                
                self._logger.debug(f"读取数据成功: {len(data)} 字节")
                return data
                
            except serial.SerialTimeoutException:
                # 超时不算错误，返回空数据
                return b''
            except serial.SerialException as e:
                self._handle_connection_error(e)
                raise SerialReadError(f"串口读取失败: {e}")
            except Exception as e:
                self._stats['error_count'] += 1
                raise SerialReadError(f"读取数据时发生未知错误: {e}")

    def read_available_data(self) -> bytes:
        """
        读取所有可用数据

        Returns:
            读取到的所有可用字节数据
        """
        if not self.is_connected():
            return b''

        try:
            available_bytes = self._serial_connection.in_waiting
            if available_bytes > 0:
                return self.read_data(available_bytes)
            return b''
        except Exception as e:
            self._logger.warning(f"读取可用数据失败: {e}")
            return b''

    def is_connected(self) -> bool:
        """
        检查串口是否已连接

        Returns:
            是否已连接
        """
        with self._lock:
            return (self._connection_state == ConnectionState.CONNECTED and
                    self._serial_connection is not None and
                    self._serial_connection.is_open)

    def get_connection_state(self) -> ConnectionState:
        """
        获取连接状态

        Returns:
            当前连接状态
        """
        with self._lock:
            return self._connection_state

    def get_config(self) -> SerialConfig:
        """
        获取串口配置

        Returns:
            串口配置对象
        """
        return self._config

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            统计信息字典
        """
        with self._lock:
            return self._stats.copy()

    def reset_stats(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._stats = {
                'bytes_sent': 0,
                'bytes_received': 0,
                'connection_count': self._stats['connection_count'],  # 保留连接次数
                'error_count': 0,
                'last_activity': None
            }
            self._logger.info("统计信息已重置")

    def add_state_callback(self, name: str, callback: Callable[[ConnectionState], None]) -> None:
        """
        添加状态变化回调

        Args:
            name: 回调名称
            callback: 回调函数，接收ConnectionState参数
        """
        with self._lock:
            self._state_callbacks[name] = callback
            self._logger.debug(f"添加状态回调: {name}")

    def remove_state_callback(self, name: str) -> None:
        """
        移除状态变化回调

        Args:
            name: 回调名称
        """
        with self._lock:
            if name in self._state_callbacks:
                del self._state_callbacks[name]
                self._logger.debug(f"移除状态回调: {name}")

    def reconnect(self, max_attempts: int = 3, delay: float = 1.0) -> bool:
        """
        重新连接串口

        Args:
            max_attempts: 最大重试次数
            delay: 重试间隔（秒）

        Returns:
            重连是否成功
        """
        with self._lock:
            if self._connection_state == ConnectionState.CONNECTED:
                return True

            self._set_connection_state(ConnectionState.RECONNECTING)

            for attempt in range(max_attempts):
                try:
                    self._logger.info(f"重连尝试 {attempt + 1}/{max_attempts}")

                    # 先断开现有连接
                    self.disconnect()

                    # 等待一段时间
                    if delay > 0:
                        time.sleep(delay)

                    # 尝试连接
                    if self.connect():
                        self._logger.info("重连成功")
                        return True

                except Exception as e:
                    self._logger.warning(f"重连尝试 {attempt + 1} 失败: {e}")
                    if attempt < max_attempts - 1:
                        time.sleep(delay)

            self._set_connection_state(ConnectionState.ERROR)
            self._logger.error(f"重连失败，已尝试 {max_attempts} 次")
            return False

    def _set_connection_state(self, new_state: ConnectionState) -> None:
        """
        设置连接状态并触发回调

        Args:
            new_state: 新的连接状态
        """
        old_state = self._connection_state
        self._connection_state = new_state

        if old_state != new_state:
            self._logger.debug(f"连接状态变化: {old_state.value} -> {new_state.value}")

            # 触发状态回调
            for name, callback in self._state_callbacks.items():
                try:
                    callback(new_state)
                except Exception as e:
                    self._logger.error(f"状态回调 {name} 执行失败: {e}")

    def _handle_connection_error(self, error: Exception) -> None:
        """
        处理连接错误

        Args:
            error: 错误对象
        """
        self._last_error = error
        self._connection_attempts += 1
        self._stats['error_count'] += 1
        self._set_connection_state(ConnectionState.ERROR)

        self._logger.error(f"连接错误 (尝试次数: {self._connection_attempts}): {error}")

    def _get_parity_constant(self, parity_str: str) -> str:
        """
        获取pyserial的校验位常量

        Args:
            parity_str: 校验位字符串

        Returns:
            pyserial校验位常量
        """
        parity_map = {
            'none': serial.PARITY_NONE,
            'even': serial.PARITY_EVEN,
            'odd': serial.PARITY_ODD,
            'mark': serial.PARITY_MARK,
            'space': serial.PARITY_SPACE
        }
        return parity_map.get(parity_str.lower(), serial.PARITY_NONE)

    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()
        # 不处理异常，让异常正常传播
        return False

    def __str__(self) -> str:
        """字符串表示"""
        return f"SerialManager(port={self._config.port}, state={self._connection_state.value})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"SerialManager(name={self._name}, port={self._config.port}, "
                f"baudrate={self._config.baudrate}, state={self._connection_state.value})")
