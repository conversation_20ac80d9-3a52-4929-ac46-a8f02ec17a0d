#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集系统主程序入口
基于五层架构的动态配置数据采集系统
支持用户交互式选择自由串口协议配置文件

作者: LD (Lead Developer)
创建时间: 2025-08-05
更新时间: 2025-08-08
版本: 2.0
"""

import sys
import os
import signal
import time
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from business_logic.protocol_flow_controller import ProtocolFlowController
from business_logic.logger_manager import LoggerManager, LogLevel, LogFormat
from utils.exceptions import (
    ConfigFileNotFoundError, ConfigParsingError, ConfigValidationError,
    SerialConnectionError, DataProcessingError
)


class DataStudioCLI:
    """数据采集系统命令行界面"""

    def __init__(self):
        """初始化CLI"""
        self.logger_manager = None
        self.logger = None
        self.flow_controller = None
        self.running = False

        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n\n🛑 接收到停止信号 ({signum})，正在优雅停止...")
        self.running = False
        if self.flow_controller:
            try:
                self.flow_controller.stop_continuous_mode()
                self.flow_controller.disconnect()
                print("✅ 设备已断开连接")
            except Exception as e:
                print(f"⚠️ 断开连接时出现异常: {e}")

        if self.logger_manager:
            try:
                self.logger_manager.shutdown()
                print("✅ 日志系统已关闭")
            except Exception as e:
                print(f"⚠️ 关闭日志系统时出现异常: {e}")

        print("👋 程序已退出")
        sys.exit(0)

    def print_header(self):
        """打印程序头部信息"""
        print("🚀 DataStudio - 自由串口数据采集系统")
        print("=" * 60)
        print("📋 功能特性:")
        print("  • 🔧 支持自定义JSON协议配置")
        print("  • 🔗 动态串口连接和管理")
        print("  • ⚙️  自动执行协议初始化流程")
        print("  • 📊 实时数据采集和解析")
        print("  • 📈 连续数据模式支持")
        print("  • 🛑 优雅停止和资源清理")
        print("  • 📝 完整的日志记录和错误处理")
        print("=" * 60)

    def initialize_logging(self) -> bool:
        """初始化日志系统"""
        try:
            log_config = {
                "log_level": LogLevel.INFO,
                "log_format": LogFormat.DETAILED,
                "log_dir": "logs",
                "enable_console": True,
                "enable_file": True,
                "enable_performance": True
            }

            self.logger_manager = LoggerManager(log_config)
            self.logger = self.logger_manager.get_logger("DataStudioCLI")

            print("✅ 日志系统初始化成功")
            self.logger.info("DataStudio CLI 启动")
            return True

        except Exception as e:
            print(f"❌ 日志系统初始化失败: {e}")
            return False

    def get_protocol_config_path(self) -> Optional[str]:
        """获取用户输入的协议配置文件路径"""
        print("\n📁 协议配置文件选择")
        print("-" * 30)

        # 显示示例配置文件
        protocols_dir = Path("config/protocols")
        if protocols_dir.exists():
            print("💡 可用的示例配置文件:")
            example_files = list(protocols_dir.glob("*.json"))
            if example_files:
                for i, file_path in enumerate(example_files, 1):
                    print(f"  {i}. {file_path}")
            else:
                print("  (未找到示例配置文件)")

        print("\n📝 请选择输入方式:")
        print("  1. 输入配置文件的完整路径")
        print("  2. 输入配置文件名(在config/protocols/目录下)")
        print("  3. 使用示例配置文件")
        print("  0. 退出程序")

        while True:
            try:
                choice = input("\n👉 请选择 (0-3): ").strip()

                if choice == "0":
                    print("👋 用户选择退出")
                    return None

                elif choice == "1":
                    # 完整路径输入
                    path_input = input("📂 请输入配置文件的完整路径: ").strip()
                    if not path_input:
                        print("❌ 路径不能为空，请重新输入")
                        continue

                    config_path = Path(path_input)
                    if not config_path.exists():
                        print(f"❌ 文件不存在: {config_path}")
                        continue

                    if not config_path.suffix.lower() == '.json':
                        print("❌ 文件必须是JSON格式(.json)")
                        continue

                    return str(config_path)

                elif choice == "2":
                    # 文件名输入
                    filename = input("📄 请输入配置文件名(如: my_protocol.json): ").strip()
                    if not filename:
                        print("❌ 文件名不能为空，请重新输入")
                        continue

                    if not filename.endswith('.json'):
                        filename += '.json'

                    config_path = protocols_dir / filename
                    if not config_path.exists():
                        print(f"❌ 文件不存在: {config_path}")
                        continue

                    return str(config_path)

                elif choice == "3":
                    # 使用示例配置文件
                    if not example_files:
                        print("❌ 没有可用的示例配置文件")
                        continue

                    if len(example_files) == 1:
                        return str(example_files[0])

                    print("\n📋 请选择示例配置文件:")
                    for i, file_path in enumerate(example_files, 1):
                        print(f"  {i}. {file_path.name}")

                    try:
                        file_choice = int(input("👉 请选择文件编号: ").strip())
                        if 1 <= file_choice <= len(example_files):
                            return str(example_files[file_choice - 1])
                        else:
                            print("❌ 无效的文件编号")
                            continue
                    except ValueError:
                        print("❌ 请输入有效的数字")
                        continue

                else:
                    print("❌ 无效的选择，请输入0-3之间的数字")
                    continue

            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                return None
            except Exception as e:
                print(f"❌ 输入处理异常: {e}")
                continue

    def load_protocol_config(self, config_path: str) -> bool:
        """加载协议配置文件"""
        try:
            print(f"\n🔧 正在加载协议配置: {config_path}")

            # 初始化协议流程控制器
            self.flow_controller = ProtocolFlowController(config_path)

            # 获取协议信息
            protocol_info = self.flow_controller.get_protocol_info()

            print("✅ 协议配置加载成功")
            print(f"📋 协议信息:")
            print(f"   名称: {protocol_info['name']}")
            print(f"   描述: {protocol_info['description']}")
            print(f"   版本: {protocol_info['version']}")
            print(f"   串口: {protocol_info['serial_config']['port']}")
            print(f"   波特率: {protocol_info['serial_config']['baudrate']}")
            print(f"   单次指令数: {protocol_info['single_commands_count']}")
            print(f"   连续指令数: {protocol_info['continuous_commands_count']}")

            self.logger.info(f"协议配置加载成功: {config_path}")
            return True

        except ConfigFileNotFoundError as e:
            print(f"❌ 配置文件未找到: {e}")
            self.logger.error(f"配置文件未找到: {e}")
            return False
        except ConfigParsingError as e:
            print(f"❌ 配置文件解析失败: {e}")
            self.logger.error(f"配置文件解析失败: {e}")
            return False
        except ConfigValidationError as e:
            print(f"❌ 配置文件验证失败: {e}")
            self.logger.error(f"配置文件验证失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 加载协议配置异常: {e}")
            self.logger.error(f"加载协议配置异常: {e}")
            return False

    def connect_device(self) -> bool:
        """连接设备"""
        try:
            print("\n🔌 正在连接设备...")

            if not self.flow_controller.connect():
                print("❌ 设备连接失败")
                return False

            print("✅ 设备连接成功")
            self.logger.info("设备连接成功")
            return True

        except SerialConnectionError as e:
            print(f"❌ 串口连接错误: {e}")
            self.logger.error(f"串口连接错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 设备连接异常: {e}")
            self.logger.error(f"设备连接异常: {e}")
            return False

    def execute_protocol_flow(self) -> bool:
        """执行协议流程"""
        try:
            print("\n⚙️ 正在执行协议初始化流程...")

            if not self.flow_controller.execute_protocol_flow():
                print("❌ 协议流程执行失败")
                return False

            print("✅ 协议初始化流程执行成功")
            self.logger.info("协议初始化流程执行成功")
            return True

        except DataProcessingError as e:
            print(f"❌ 数据处理错误: {e}")
            self.logger.error(f"数据处理错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 协议流程执行异常: {e}")
            self.logger.error(f"协议流程执行异常: {e}")
            return False

    def start_continuous_mode(self) -> bool:
        """启动连续数据模式"""
        try:
            print("\n📊 检查是否支持连续数据模式...")

            protocol_info = self.flow_controller.get_protocol_info()
            if protocol_info['continuous_commands_count'] == 0:
                print("ℹ️ 当前协议不支持连续数据模式")
                return True

            print("🚀 启动连续数据采集模式...")

            # 设置数据回调
            self.flow_controller.set_data_callback(self._data_callback)

            # 启动连续模式
            if not self.flow_controller.start_continuous_mode():
                print("❌ 连续数据模式启动失败")
                return False

            print("✅ 连续数据采集已启动")
            print("📊 实时数据显示:")
            print("   按 Ctrl+C 停止数据采集")
            print("-" * 60)

            self.running = True
            self.logger.info("连续数据模式启动成功")
            return True

        except Exception as e:
            print(f"❌ 启动连续模式异常: {e}")
            self.logger.error(f"启动连续模式异常: {e}")
            return False

    def _data_callback(self, data_result):
        """数据回调函数"""
        try:
            if not self.running:
                return

            # 简单的数据显示
            timestamp = time.strftime("%H:%M:%S", time.localtime(data_result.timestamp))
            frame_info = f"帧#{data_result.frame_number:06d}"

            # 显示解析的字段数据
            if data_result.parsed_fields:
                field_info = []
                for field in data_result.parsed_fields:
                    field_info.append(f"{field.field_name}={field.scaled_value:.3f}{field.unit}")

                fields_str = " | ".join(field_info[:6])  # 最多显示6个字段
                if len(data_result.parsed_fields) > 6:
                    fields_str += " | ..."

                print(f"[{timestamp}] {frame_info} | {fields_str}")
            else:
                print(f"[{timestamp}] {frame_info} | 原始数据: {len(data_result.frame_data)} 字节")

        except Exception as e:
            if self.logger:
                self.logger.error(f"数据回调异常: {e}")

    def wait_for_user_stop(self):
        """等待用户停止"""
        try:
            while self.running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.running = False

    def cleanup(self):
        """清理资源"""
        try:
            if self.flow_controller:
                print("\n🛑 正在停止数据采集...")
                self.flow_controller.stop_continuous_mode()
                self.flow_controller.disconnect()
                print("✅ 设备已断开连接")

            if self.logger_manager:
                self.logger_manager.shutdown()
                print("✅ 日志系统已关闭")

        except Exception as e:
            print(f"⚠️ 清理资源时出现异常: {e}")

    def run(self):
        """运行主程序"""
        try:
            # 打印头部信息
            self.print_header()

            # 初始化日志系统
            if not self.initialize_logging():
                return 1

            # 获取协议配置文件路径
            config_path = self.get_protocol_config_path()
            if not config_path:
                return 0

            # 加载协议配置
            if not self.load_protocol_config(config_path):
                return 1

            # 连接设备
            if not self.connect_device():
                return 1

            # 执行协议流程
            if not self.execute_protocol_flow():
                return 1

            # 启动连续数据模式
            if not self.start_continuous_mode():
                return 1

            # 等待用户停止
            if self.running:
                self.wait_for_user_stop()

            return 0

        except KeyboardInterrupt:
            print("\n\n👋 用户中断程序")
            return 0
        except Exception as e:
            print(f"\n❌ 程序运行异常: {e}")
            if self.logger:
                self.logger.error(f"程序运行异常: {e}")
            return 1
        finally:
            self.cleanup()


def main():
    """主程序入口"""
    cli = DataStudioCLI()
    exit_code = cli.run()
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
